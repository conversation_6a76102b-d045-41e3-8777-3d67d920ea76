import React from "react";
import { GoArrowUpRight } from "react-icons/go";
import { <PERSON><PERSON>inked<PERSON>, LiaLinkedinIn } from "react-icons/lia";
import { Link } from "react-router-dom";

const Footer = () => {
  return (
    <div className="flex items-center px-7 py-5 bg-[#060a21] text-white border-t border-white/50 justify-between">
      <p>Created by <PERSON><PERSON> </p>
      <div className="flex items-center gap-4">
        <LiaLinkedin
          onClick={() => {
            window.open(
              "https://www.linkedin.com/in/oufkir-hamza-a5920324b/",
              "_blank"
            );
          }}
          className="w-7 h-7 cursor-pointer"
        />
        <svg
          onClick={() => {
            window.open("https://github.com/oufkirhamza", "_blank");
          }}
          xmlns="http://www.w3.org/2000/svg"
          className="w-6 h-6   cursor-pointer"
          fill="none"
          viewBox="0 0 100 100"
        >
          <path
            fill="#fff"
            d="M50 1C22.39 1 0 23.386 0 51c0 22.092 14.326 40.834 34.193 47.446 2.499.462 3.416-1.085 3.416-2.406 0-1.192-.046-5.131-.067-9.309-13.91 3.025-16.846-5.9-16.846-5.9-2.274-5.779-5.552-7.315-5.552-7.315-4.536-3.104.342-3.04.342-3.04 5.021.353 7.665 5.153 7.665 5.153 4.46 7.644 11.697 5.434 14.55 4.156.449-3.232 1.745-5.437 3.175-6.686-11.106-1.264-22.78-5.552-22.78-24.71 0-5.459 1.953-9.92 5.151-13.42-.519-1.26-2.23-6.346.485-13.233 0 0 4.198-1.344 13.753 5.125 3.988-1.108 8.266-1.663 12.515-1.682 4.25.019 8.53.574 12.526 1.682 9.543-6.469 13.736-5.125 13.736-5.125 2.722 6.887 1.01 11.973.49 13.232 3.206 3.502 5.146 7.962 5.146 13.42 0 19.205-11.697 23.434-22.83 24.671 1.793 1.552 3.39 4.595 3.39 9.26 0 6.69-.057 12.074-.057 13.721 0 1.33.9 2.89 3.434 2.399C85.691 91.819 100 73.085 100 51c0-27.614-22.386-50-50-50"
          />
          <path
            fill="#fff"
            d="M18.727 72.227c-.11.248-.502.322-.857.152-.363-.163-.567-.502-.45-.751.108-.256.5-.327.862-.156.363.163.57.505.445.755m2.459 2.194c-.238.221-.705.118-1.021-.231-.327-.349-.388-.814-.146-1.04.245-.22.698-.117 1.025.232.328.353.391.816.142 1.04zm1.687 2.808c-.306.213-.807.013-1.117-.432-.306-.444-.306-.977.007-1.191.31-.214.804-.021 1.117.42.306.452.306.985-.007 1.203m2.854 3.252c-.274.302-.858.22-1.285-.192-.437-.403-.56-.975-.284-1.277.277-.303.864-.218 1.294.191.435.403.567.979.275 1.278m3.687 1.098c-.12.391-.682.57-1.249.403-.565-.171-.935-.63-.821-1.026.117-.394.682-.58 1.253-.401.564.17.935.625.818 1.024m4.198.465c.014.413-.466.755-1.06.762-.599.013-1.082-.32-1.089-.726 0-.416.47-.755 1.067-.765.595-.012 1.082.32 1.082.73m4.123-.158c.071.403-.342.816-.932.926-.58.106-1.118-.143-1.192-.541-.072-.413.349-.826.928-.933.591-.103 1.12.14 1.196.548"
          />
        </svg>
      </div>
    </div>
  );
};

export default Footer;
